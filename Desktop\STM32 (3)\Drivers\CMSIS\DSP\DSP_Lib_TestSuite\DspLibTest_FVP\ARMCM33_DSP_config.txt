# Parameters:
# instance.parameter=value       #(type, mode) default = 'def value' : description : [min..max]
#----------------------------------------------------------------------------------------------
cpu0.FPU=0                                            # (bool  , init-time) default = '1'      : Set whether the model has VFP support
cpu0.DSP=1                                            # (bool  , init-time) default = '1'      : Set whether the model has the DSP extension
cpu0.semihosting-enable=0                             # (bool  , init-time) default = '1'      : Enable semihosting SVC traps. Applications that do not use semihosting must set this parameter to false.
cpu0.MPU_S=0x8                                        # (int   , init-time) default = '0x8'    : Number of regions in the Secure MPU. If Security Extentions are absent, this is ignored : [0x0..0x10]
cpu0.MPU_NS=0x8                                       # (int   , init-time) default = '0x8'    : Number of regions in the Non-Secure MPU. If Security Extentions are absent, this is the total number of MPU regions : [0x0..0x10]
cpu0.ITM=0                                            # (bool  , init-time) default = '1'      : Level of instrumentation trace supported. false : No ITM trace included, true: ITM trace included
cpu0.IRQLVL=0x3                                       # (int   , init-time) default = '0x3'    : Number of bits of interrupt priority : [0x3..0x8]
cpu0.BIGENDINIT=0                                     # (bool  , init-time) default = '0'      : Initialize processor to big endian mode
cpu0.INITSVTOR=0x00000000                             # (int   , init-time) default = '0x10000000' : Secure vector-table offset at reset : [0x0..0xFFFFFF80]
cpu0.INITNSVTOR=0x0                                   # (int   , init-time) default = '0x0'    : Non-Secure vector-table offset at reset : [0x0..0xFFFFFF80]
cpu0.SAU=0x0                                          # (int   , init-time) default = '0x4'    : Number of SAU regions (0 => no SAU) : [0x0..0x8]
cpu0.SAU_CTRL.ENABLE=0                                # (bool  , init-time) default = '0'      : Enable SAU at reset
cpu0.SAU_CTRL.ALLNS=0                                 # (bool  , init-time) default = '0'      : At reset, the SAU treats entire memory space as NS when the SAU is disabled if this is set
cpu0.NUM_IDAU_REGION=0x0                              # (int   , init-time) default = '0xA'    : 
cpu0.LOCK_SAU=0                                       # (bool  , init-time) default = '0'      : Lock down of SAU registers write
cpu0.LOCK_S_MPU=0                                     # (bool  , init-time) default = '0'      : Lock down of Secure MPU registers write
cpu0.LOCK_NS_MPU=0                                    # (bool  , init-time) default = '0'      : Lock down of Non-Secure MPU registers write
cpu0.CPIF=1                                           # (bool  , init-time) default = '1'      : Specifies whether the external coprocessor interface is included
cpu0.SECEXT=0                                         # (bool  , init-time) default = '1'      : Whether the ARMv8-M Security Extensions are included
fvp_mps2.DISABLE_GATING=1                             # (bool  , init-time) default = '0'      : Disable Memory gating logic
fvp_mps2.NUM_IDAU_REGION=0x0                          # (int   , init-time) default = '0xA'    : 
fvp_mps2.SCC_ID.Variant=0x0                           # (int   , init-time) default = '0x0'    : SCC_ID[23:20], X in the FGPA version 'rXpY' : [0x0..0xF]
fvp_mps2.SCC_ID.Revision=0x1                          # (int   , init-time) default = '0x1'    : SCC_ID[3:0], Y in the FGPA version 'rXpY' : [0x0..0xF]
fvp_mps2.platform_type=0x0                            # (int   , init-time) default = '0x0'    : 0:MPS2 ; 1:IoT Kit ; 2:Castor : [0x0..0x2]
fvp_mps2.extra_psram=0                                # (bool  , init-time) default = '0'      : Increases PSRAM to 32Mb
fvp_mps2.UART2.out_file=""                            # (string, init-time) default = ''       : Output file to hold data written by the UART (use '-' to send all output to stdout)
fvp_mps2.UART2.in_file=""                             # (string, init-time) default = ''       : Input file for data to be read by the UART
fvp_mps2.UART2.unbuffered_output=0                    # (bool  , init-time) default = '0'      : Unbuffered output
fvp_mps2.UART2.in_file_escape_sequence="##"           # (string, init-time) default = '##'     : Input file escape sequence
fvp_mps2.UART2.shutdown_on_eot=0                      # (bool  , init-time) default = '0'      : Shutdown simulation when a EOT (ASCII 4) char is transmitted (useful for regression tests when semihosting is not available)
fvp_mps2.UART2.shutdown_tag=""                        # (string, run-time ) default = ''       : Shutdown simulation when a string is transmitted
fvp_mps2.UART1.out_file=""                            # (string, init-time) default = ''       : Output file to hold data written by the UART (use '-' to send all output to stdout)
fvp_mps2.UART1.in_file=""                             # (string, init-time) default = ''       : Input file for data to be read by the UART
fvp_mps2.UART1.unbuffered_output=0                    # (bool  , init-time) default = '0'      : Unbuffered output
fvp_mps2.UART1.in_file_escape_sequence="##"           # (string, init-time) default = '##'     : Input file escape sequence
fvp_mps2.UART1.shutdown_on_eot=0                      # (bool  , init-time) default = '0'      : Shutdown simulation when a EOT (ASCII 4) char is transmitted (useful for regression tests when semihosting is not available)
fvp_mps2.UART1.shutdown_tag=""                        # (string, run-time ) default = ''       : Shutdown simulation when a string is transmitted
fvp_mps2.mps2_visualisation.rate_limit-enable=1       # (bool  , init-time) default = '1'      : Rate limit simulation.
fvp_mps2.mps2_visualisation.disable-visualisation=0   # (bool  , init-time) default = '0'      : Enable/disable visualisation
fvp_mps2.mps2_visualisation.window_title="CLCD %cpu%"  # (string, init-time) default = 'CLCD %cpu%' : Window title (%cpu% is replaced by cpu_name)
fvp_mps2.mps2_visualisation.idler.delay_ms=0x32       # (int   , init-time) default = '0x32'   : Determines the period, in milliseconds of real time, between gui_callback() calls.
fvp_mps2.telnetterminal0.mode="telnet"                # (string, init-time) default = 'telnet' : Terminal initialisation mode
fvp_mps2.telnetterminal0.start_telnet=1               # (bool  , init-time) default = '1'      : Start telnet if nothing connected
fvp_mps2.telnetterminal0.start_port=0x1388            # (int   , init-time) default = '0x1388' : Telnet TCP Port Number : [0x0..0xFFFFFFFF]
fvp_mps2.telnetterminal0.quiet=0                      # (bool  , init-time) default = '0'      : Avoid output on stdout/stderr
fvp_mps2.telnetterminal0.terminal_command=""          # (string, init-time) default = ''       : Commandline to launch a terminal application and connect to the opened TCP port. Keywords %port and %title will be replaced with the opened port number and component name respectively. An empty string (default behaviour) will launch xterm (Linux) or telnet.exe (Windows)
fvp_mps2.telnetterminal1.mode="telnet"                # (string, init-time) default = 'telnet' : Terminal initialisation mode
fvp_mps2.telnetterminal1.start_telnet=1               # (bool  , init-time) default = '1'      : Start telnet if nothing connected
fvp_mps2.telnetterminal1.start_port=0x1388            # (int   , init-time) default = '0x1388' : Telnet TCP Port Number : [0x0..0xFFFFFFFF]
fvp_mps2.telnetterminal1.quiet=0                      # (bool  , init-time) default = '0'      : Avoid output on stdout/stderr
fvp_mps2.telnetterminal1.terminal_command=""          # (string, init-time) default = ''       : Commandline to launch a terminal application and connect to the opened TCP port. Keywords %port and %title will be replaced with the opened port number and component name respectively. An empty string (default behaviour) will launch xterm (Linux) or telnet.exe (Windows)
fvp_mps2.telnetterminal2.mode="telnet"                # (string, init-time) default = 'telnet' : Terminal initialisation mode
fvp_mps2.telnetterminal2.start_telnet=1               # (bool  , init-time) default = '1'      : Start telnet if nothing connected
fvp_mps2.telnetterminal2.start_port=0x1388            # (int   , init-time) default = '0x1388' : Telnet TCP Port Number : [0x0..0xFFFFFFFF]
fvp_mps2.telnetterminal2.quiet=0                      # (bool  , init-time) default = '0'      : Avoid output on stdout/stderr
fvp_mps2.telnetterminal2.terminal_command=""          # (string, init-time) default = ''       : Commandline to launch a terminal application and connect to the opened TCP port. Keywords %port and %title will be replaced with the opened port number and component name respectively. An empty string (default behaviour) will launch xterm (Linux) or telnet.exe (Windows)
fvp_mps2.PSRAM_M7.size=0x100000000                    # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.PSRAM_M7.fill1=0xDFDFDFCF                    # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.PSRAM_M7.fill2=0xCFDFDFDF                    # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.UART0.out_file=""                            # (string, init-time) default = ''       : Output file to hold data written by the UART (use '-' to send all output to stdout)
fvp_mps2.UART0.in_file=""                             # (string, init-time) default = ''       : Input file for data to be read by the UART
fvp_mps2.UART0.unbuffered_output=0                    # (bool  , init-time) default = '0'      : Unbuffered output
fvp_mps2.UART0.in_file_escape_sequence="##"           # (string, init-time) default = '##'     : Input file escape sequence
fvp_mps2.UART0.shutdown_on_eot=0                      # (bool  , init-time) default = '0'      : Shutdown simulation when a EOT (ASCII 4) char is transmitted (useful for regression tests when semihosting is not available)
fvp_mps2.UART0.shutdown_tag=""                        # (string, run-time ) default = ''       : Shutdown simulation when a string is transmitted
fvp_mps2.cmsdk_watchdog.simhalt=0                     # (bool  , run-time ) default = '0'      : Halt on reset.
fvp_mps2.s32k_watchdog.simhalt=0                      # (bool  , run-time ) default = '0'      : Halt on reset.
fvp_mps2.secure_watchdog.simhalt=0                    # (bool  , run-time ) default = '0'      : Halt on reset.
fvp_mps2.nonsecure_watchdog.simhalt=0                 # (bool  , run-time ) default = '0'      : Halt on reset.
fvp_mps2.PSRAM.size=0x100000000                       # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.PSRAM.fill1=0xDFDFDFCF                       # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.PSRAM.fill2=0xCFDFDFDF                       # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.ssram2.size=0x100000000                      # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.ssram2.fill1=0xDFDFDFCF                      # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.ssram2.fill2=0xCFDFDFDF                      # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.ssram1.size=0x100000000                      # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.ssram1.fill1=0xDFDFDFCF                      # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.ssram1.fill2=0xCFDFDFDF                      # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.stub.size=0x100000000                        # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.iotss_internal_sram0.size=0x100000000        # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.iotss_internal_sram0.fill1=0xDFDFDFCF        # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram0.fill2=0xCFDFDFDF        # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram1.size=0x100000000        # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.iotss_internal_sram1.fill1=0xDFDFDFCF        # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram1.fill2=0xCFDFDFDF        # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram2.size=0x100000000        # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.iotss_internal_sram2.fill1=0xDFDFDFCF        # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram2.fill2=0xCFDFDFDF        # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram3.size=0x100000000        # (int   , init-time) default = '0x100000000' : Memory Size
fvp_mps2.iotss_internal_sram3.fill1=0xDFDFDFCF        # (int   , init-time) default = '0xDFDFDFCF' : Fill pattern 1, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.iotss_internal_sram3.fill2=0xCFDFDFDF        # (int   , init-time) default = '0xCFDFDFDF' : Fill pattern 2, initialise memory at start of simulation with alternating fill1, fill2 pattern
fvp_mps2.sys_ppu.use_active_signal=0                  # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.sys_ppu.revision="r0p0"                      # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.cpu0core_ppu.revision="r0p0"                 # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.cpu0dbg_ppu.revision="r0p0"                  # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.cpu1core_ppu.use_active_signal=0             # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.cpu1core_ppu.revision="r0p0"                 # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.cpu1dbg_ppu.use_active_signal=0              # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.cpu1dbg_ppu.revision="r0p0"                  # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.crypto_ppu.use_active_signal=0               # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.crypto_ppu.revision="r0p0"                   # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.cordio_ppu.use_active_signal=0               # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.cordio_ppu.revision="r0p0"                   # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.dbg_ppu.use_active_signal=0                  # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.dbg_ppu.revision="r0p0"                      # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.ram0_ppu.use_active_signal=0                 # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.ram0_ppu.revision="r0p0"                     # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.ram1_ppu.use_active_signal=0                 # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.ram1_ppu.revision="r0p0"                     # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.ram2_ppu.use_active_signal=0                 # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.ram2_ppu.revision="r0p0"                     # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.ram3_ppu.use_active_signal=0                 # (bool  , init-time) default = '0'      : Use device-active signal
fvp_mps2.ram3_ppu.revision="r0p0"                     # (string, init-time) default = 'r0p0'   : Revision
fvp_mps2.smsc_91c111.enabled=0                        # (bool  , init-time) default = '0'      : Host interface connection enabled
fvp_mps2.smsc_91c111.mac_address="00:02:f7:ef:5d:a2"  # (string, init-time) default = '00:02:f7:ef:5d:a2' : Host/model MAC address
fvp_mps2.smsc_91c111.promiscuous=1                    # (bool  , init-time) default = '1'      : Put host into promiscuous mode
fvp_mps2.hostbridge.interfaceName="ARM0"              # (string, init-time) default = 'ARM0'   : Host Interface
fvp_mps2.hostbridge.userNetworking=0                  # (bool  , init-time) default = '0'      : Enable user-mode networking
fvp_mps2.hostbridge.userNetSubnet="***********/24"    # (string, init-time) default = '***********/24' : Virtual subnet for user-mode networking
fvp_mps2.hostbridge.userNetPorts=""                   # (string, init-time) default = ''       : Listening ports to expose in user-mode networking
fvp_mps2.secure_control_register_block.FLASH_BLOCK_CFG=0x3  # (int   , init-time) default = '0x3'    : Flash Block size configuration : [0x0..0x31]
fvp_mps2.secure_control_register_block.SRAM_BLOCK_CFG=0x3  # (int   , init-time) default = '0x3'    : SRAM Block size configuration : [0x0..0x31]
fvp_mps2.secure_control_register_block.FLASH_WATERMARK_SUPPORTED=1  # (bool  , init-time) default = '1'      : Flash Watermark supported
fvp_mps2.secure_control_register_block.SRAM_WATERMARK_SUPPORTED=1  # (bool  , init-time) default = '1'      : SRAM Watermark supported
fvp_mps2.exclusive_monitor_psram.enable_component=1   # (bool  , init-time) default = '1'      : Enable component
fvp_mps2.exclusive_monitor_psram.number_of_monitors=0x8  # (int   , init-time) default = '0x8'    : Number of monitors : [0x1..0xFFFFFFFF]
fvp_mps2.exclusive_monitor_psram.log2_granule_size=0x0  # (int   , init-time) default = '0x0'    : log2 of address granule size : [0x0..0xB]
fvp_mps2.exclusive_monitor_psram.monitor_non_excl_stores=0  # (bool  , init-time) default = '0'      : Monitor non-exclusive stores from the same master
fvp_mps2.exclusive_monitor_psram.match_secure_state=1  # (bool  , init-time) default = '1'      : Treat the secure state like an address bit
fvp_mps2.exclusive_monitor_psram.shareability_domain=0x3  # (int   , init-time) default = '0x3'    : Maximum shareability domain of interest, transactions outside of the domain will pass through un-monitored (0-non-shared, 1-inner, 2-outer, 3-system) : [0x0..0x3]
fvp_mps2.exclusive_monitor_psram.apply_access_width_criteria_to_non_excl_stores=1  # (bool  , init-time) default = '1'      : Apply the given exclusive store width matching criteria to non-exclusive stores
fvp_mps2.exclusive_monitor_zbtsram1.enable_component=1  # (bool  , init-time) default = '1'      : Enable component
fvp_mps2.exclusive_monitor_zbtsram1.number_of_monitors=0x8  # (int   , init-time) default = '0x8'    : Number of monitors : [0x1..0xFFFFFFFF]
fvp_mps2.exclusive_monitor_zbtsram1.log2_granule_size=0x0  # (int   , init-time) default = '0x0'    : log2 of address granule size : [0x0..0xB]
fvp_mps2.exclusive_monitor_zbtsram1.monitor_non_excl_stores=0  # (bool  , init-time) default = '0'      : Monitor non-exclusive stores from the same master
fvp_mps2.exclusive_monitor_zbtsram1.match_secure_state=1  # (bool  , init-time) default = '1'      : Treat the secure state like an address bit
fvp_mps2.exclusive_monitor_zbtsram1.shareability_domain=0x3  # (int   , init-time) default = '0x3'    : Maximum shareability domain of interest, transactions outside of the domain will pass through un-monitored (0-non-shared, 1-inner, 2-outer, 3-system) : [0x0..0x3]
fvp_mps2.exclusive_monitor_zbtsram1.apply_access_width_criteria_to_non_excl_stores=1  # (bool  , init-time) default = '1'      : Apply the given exclusive store width matching criteria to non-exclusive stores
fvp_mps2.exclusive_monitor_zbtsram2.enable_component=1  # (bool  , init-time) default = '1'      : Enable component
fvp_mps2.exclusive_monitor_zbtsram2.number_of_monitors=0x8  # (int   , init-time) default = '0x8'    : Number of monitors : [0x1..0xFFFFFFFF]
fvp_mps2.exclusive_monitor_zbtsram2.log2_granule_size=0x0  # (int   , init-time) default = '0x0'    : log2 of address granule size : [0x0..0xB]
fvp_mps2.exclusive_monitor_zbtsram2.monitor_non_excl_stores=0  # (bool  , init-time) default = '0'      : Monitor non-exclusive stores from the same master
fvp_mps2.exclusive_monitor_zbtsram2.match_secure_state=1  # (bool  , init-time) default = '1'      : Treat the secure state like an address bit
fvp_mps2.exclusive_monitor_zbtsram2.shareability_domain=0x3  # (int   , init-time) default = '0x3'    : Maximum shareability domain of interest, transactions outside of the domain will pass through un-monitored (0-non-shared, 1-inner, 2-outer, 3-system) : [0x0..0x3]
fvp_mps2.exclusive_monitor_zbtsram2.apply_access_width_criteria_to_non_excl_stores=1  # (bool  , init-time) default = '1'      : Apply the given exclusive store width matching criteria to non-exclusive stores
fvp_mps2.exclusive_monitor_iotss_internal_sram.enable_component=1  # (bool  , init-time) default = '1'      : Enable component
fvp_mps2.exclusive_monitor_iotss_internal_sram.number_of_monitors=0x8  # (int   , init-time) default = '0x8'    : Number of monitors : [0x1..0xFFFFFFFF]
fvp_mps2.exclusive_monitor_iotss_internal_sram.log2_granule_size=0x0  # (int   , init-time) default = '0x0'    : log2 of address granule size : [0x0..0xB]
fvp_mps2.exclusive_monitor_iotss_internal_sram.monitor_non_excl_stores=0  # (bool  , init-time) default = '0'      : Monitor non-exclusive stores from the same master
fvp_mps2.exclusive_monitor_iotss_internal_sram.match_secure_state=1  # (bool  , init-time) default = '1'      : Treat the secure state like an address bit
fvp_mps2.exclusive_monitor_iotss_internal_sram.shareability_domain=0x3  # (int   , init-time) default = '0x3'    : Maximum shareability domain of interest, transactions outside of the domain will pass through un-monitored (0-non-shared, 1-inner, 2-outer, 3-system) : [0x0..0x3]
fvp_mps2.exclusive_monitor_iotss_internal_sram.apply_access_width_criteria_to_non_excl_stores=1  # (bool  , init-time) default = '1'      : Apply the given exclusive store width matching criteria to non-exclusive stores
fvp_mps2.dma0_securitymodifier.behaviour_ns_to_s=0x0  # (int   , init-time) default = '0x0'    : Behaviour for NS transactions to S space : 0:block 1:transmit 2:convert to S
fvp_mps2.dma0_securitymodifier.behaviour_s_to_ns=0x0  # (int   , init-time) default = '0x0'    : Behaviour for S transactions to NS space : 0:block 1:transmit 2:convert to NS
fvp_mps2.dma1_securitymodifier.behaviour_ns_to_s=0x0  # (int   , init-time) default = '0x0'    : Behaviour for NS transactions to S space : 0:block 1:transmit 2:convert to S
fvp_mps2.dma1_securitymodifier.behaviour_s_to_ns=0x0  # (int   , init-time) default = '0x0'    : Behaviour for S transactions to NS space : 0:block 1:transmit 2:convert to NS
fvp_mps2.dma2_securitymodifier.behaviour_ns_to_s=0x0  # (int   , init-time) default = '0x0'    : Behaviour for NS transactions to S space : 0:block 1:transmit 2:convert to S
fvp_mps2.dma2_securitymodifier.behaviour_s_to_ns=0x0  # (int   , init-time) default = '0x0'    : Behaviour for S transactions to NS space : 0:block 1:transmit 2:convert to NS
fvp_mps2.dma3_securitymodifier.behaviour_ns_to_s=0x0  # (int   , init-time) default = '0x0'    : Behaviour for NS transactions to S space : 0:block 1:transmit 2:convert to S
fvp_mps2.dma3_securitymodifier.behaviour_s_to_ns=0x0  # (int   , init-time) default = '0x0'    : Behaviour for S transactions to NS space : 0:block 1:transmit 2:convert to NS
fvp_mps2.dma0.fifo_size=0x10                          # (int   , init-time) default = '0x10'   : Channel FIFO size in bytes
fvp_mps2.dma0.max_transfer=0x100                      # (int   , init-time) default = '0x100'  : Largest atomic transfer
fvp_mps2.dma0.generate_clear=0                        # (bool  , init-time) default = '0'      : Generate clear response
fvp_mps2.dma0.activate_delay=0x0                      # (int   , init-time) default = '0x0'    : request delay
fvp_mps2.dma1.fifo_size=0x10                          # (int   , init-time) default = '0x10'   : Channel FIFO size in bytes
fvp_mps2.dma1.max_transfer=0x100                      # (int   , init-time) default = '0x100'  : Largest atomic transfer
fvp_mps2.dma1.generate_clear=0                        # (bool  , init-time) default = '0'      : Generate clear response
fvp_mps2.dma1.activate_delay=0x0                      # (int   , init-time) default = '0x0'    : request delay
fvp_mps2.dma2.fifo_size=0x10                          # (int   , init-time) default = '0x10'   : Channel FIFO size in bytes
fvp_mps2.dma2.max_transfer=0x100                      # (int   , init-time) default = '0x100'  : Largest atomic transfer
fvp_mps2.dma2.generate_clear=0                        # (bool  , init-time) default = '0'      : Generate clear response
fvp_mps2.dma2.activate_delay=0x0                      # (int   , init-time) default = '0x0'    : request delay
fvp_mps2.dma3.fifo_size=0x10                          # (int   , init-time) default = '0x10'   : Channel FIFO size in bytes
fvp_mps2.dma3.max_transfer=0x100                      # (int   , init-time) default = '0x100'  : Largest atomic transfer
fvp_mps2.dma3.generate_clear=0                        # (bool  , init-time) default = '0'      : Generate clear response
fvp_mps2.dma3.activate_delay=0x0                      # (int   , init-time) default = '0x0'    : request delay
fvp_mps2.iotss_systemcontrol.cpu0wait=0               # (bool  , init-time) default = '0'      : Whether to hold cpu1 in reset at boot
fvp_mps2.iotss_systemcontrol.cpu1wait=1               # (bool  , init-time) default = '1'      : Whether to hold cpu1 in reset at boot
fvp_mps2.iotss_cpuidentity.debugger_master_id=0xFFFFFFFF  # (int   , init-time) default = '0xFFFFFFFF' :  : [0x0..0xFFFFFFFF]
#----------------------------------------------------------------------------------------------
